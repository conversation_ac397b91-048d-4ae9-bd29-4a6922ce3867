import React, { useState, useEffect } from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Box } from "@/components/ui/box";
import { Icon } from "@/components/ui/icon";
import { Switch } from "@/components/ui/switch";
import { Badge, BadgeText, BadgeIcon } from "@/components/ui/badge";
import { Pressable } from "@/components/ui/pressable";
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from "@/components/ui/select";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from "@/components/ui/actionsheet";
import { TimeSlotSelector } from "./time-slot-selector";
import { X, ChevronDown, Check } from "lucide-react-native";
import { ArrowDown2 } from "iconsax-react-nativejs";
import { AvailableTimeSlot } from "@/data/screens/activities/types";

// Filter field types
export interface SelectOption {
  label: string;
  value: string;
}

export interface MultiSelectField {
  type: "multiselect";
  key: string;
  label: string;
  options: SelectOption[];
  placeholder?: string;
}

export interface SingleSelectField {
  type: "select";
  key: string;
  label: string;
  options: SelectOption[];
  placeholder?: string;
}

export interface TimeField {
  type: "time";
  key: string;
  label: string;
  timeSlots: AvailableTimeSlot[];
  placeholder?: string;
}

export interface ToggleField {
  type: "toggle";
  key: string;
  label: string;
  description?: string;
}

export type FilterField =
  | MultiSelectField
  | SingleSelectField
  | TimeField
  | ToggleField;

export interface FilterValues {
  [key: string]: string | string[] | boolean;
}

export interface FilterComponentProps {
  isOpen: boolean;
  onClose: () => void;
  fields: FilterField[];
  values: FilterValues;
  onValuesChange: (values: FilterValues) => void;
  onApply: (values: FilterValues) => void;
  onReset: () => void;
  title?: string;
}

// Removable tag component for multi-select values
const RemovableTag = ({
  label,
  onRemove,
}: {
  label: string;
  onRemove: () => void;
}) => (
  <Badge
    variant="outline"
    className="bg-[#E6F9FC] border-[#00BFE0] border-2 border-dashed rounded-lg px-3 py-2 mr-2 mb-2"
  >
    <BadgeText className="text-[#00BFE0] font-dm-sans-medium text-sm mr-1">
      {label}
    </BadgeText>
    <Pressable onPress={onRemove}>
      <BadgeIcon as={X} size="xs" className="text-[#00BFE0]" />
    </Pressable>
  </Badge>
);

// Multi-select field component
const MultiSelectFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: MultiSelectField;
  value: string[];
  onChange: (value: string[]) => void;
}) => {
  const selectedOptions = field.options.filter((option) =>
    value.includes(option.value)
  );

  const handleOptionToggle = (optionValue: string) => {
    const newValue = value.includes(optionValue)
      ? value.filter((v) => v !== optionValue)
      : [...value, optionValue];
    onChange(newValue);
  };

  return (
    <VStack space="sm">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        {field.label}
      </Text>

      {/* Selected tags */}
      {selectedOptions.length > 0 && (
        <Box className="bg-[#E6F9FC] border-[#00BFE0] border-2 border-dashed rounded-lg p-3 flex-row flex-wrap">
          {selectedOptions.map((option) => (
            <RemovableTag
              key={option.value}
              label={option.label}
              onRemove={() => handleOptionToggle(option.value)}
            />
          ))}
        </Box>
      )}

      {/* Options list */}
      <VStack space="xs">
        {field.options.map((option) => (
          <Pressable
            key={option.value}
            onPress={() => handleOptionToggle(option.value)}
            className="flex-row items-center py-2"
          >
            <Box
              className={`w-5 h-5 rounded border-2 mr-3 items-center justify-center ${
                value.includes(option.value)
                  ? "bg-[#00BFE0] border-[#00BFE0]"
                  : "border-typography-300"
              }`}
            >
              {value.includes(option.value) && (
                <Icon as={Check} size="xs" className="text-white" />
              )}
            </Box>
            <Text className="text-typography-700 font-dm-sans-regular text-base flex-1">
              {option.label}
            </Text>
          </Pressable>
        ))}
      </VStack>
    </VStack>
  );
};

// Single select field component
const SingleSelectFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: SingleSelectField;
  value: string;
  onChange: (value: string) => void;
}) => (
  <VStack space="sm">
    <Text className="text-typography-700 font-dm-sans-medium text-base">
      {field.label}
    </Text>
    <Select selectedValue={value} onValueChange={onChange}>
      <SelectTrigger className="bg-background-50 border border-outline-200 rounded-lg px-4 py-3">
        <SelectInput
          placeholder={
            field.placeholder || `Select ${field.label.toLowerCase()}`
          }
          className="text-typography-700 flex-1"
        />
        <SelectIcon as={() => <ArrowDown2 size="20" color="gray" />} />
      </SelectTrigger>
      <SelectPortal>
        <SelectBackdrop />
        <SelectContent>
          <SelectDragIndicatorWrapper>
            <SelectDragIndicator />
          </SelectDragIndicatorWrapper>
          <SelectScrollView>
            {field.options.map((option) => (
              <SelectItem
                key={option.value}
                label={option.label}
                value={option.value}
              />
            ))}
          </SelectScrollView>
        </SelectContent>
      </SelectPortal>
    </Select>
  </VStack>
);

// Time field component
const TimeFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: TimeField;
  value: string;
  onChange: (value: string) => void;
}) => (
  <VStack space="sm">
    <Text className="text-typography-700 font-dm-sans-medium text-base">
      {field.label}
    </Text>
    <TimeSlotSelector
      value={value}
      onChange={onChange}
      timeSlots={field.timeSlots}
      placeholder={field.placeholder}
    />
  </VStack>
);

// Toggle field component
const ToggleFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: ToggleField;
  value: boolean;
  onChange: (value: boolean) => void;
}) => (
  <HStack className="items-center justify-between py-2">
    <VStack className="flex-1">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        {field.label}
      </Text>
      {field.description && (
        <Text className="text-typography-500 font-dm-sans-regular text-sm">
          {field.description}
        </Text>
      )}
    </VStack>
    <Switch value={value} onValueChange={onChange} size="md" />
  </HStack>
);

export const FilterComponent: React.FC<FilterComponentProps> = ({
  isOpen,
  onClose,
  fields,
  values,
  onValuesChange,
  onApply,
  onReset,
  title = "Filter",
}) => {
  const [localValues, setLocalValues] = useState<FilterValues>(values);

  useEffect(() => {
    if (isOpen) {
      setLocalValues(values);
    }
  }, [values, isOpen]);

  const handleFieldChange = (key: string, value: any) => {
    const newValues = { ...localValues, [key]: value };
    setLocalValues(newValues);
  };

  const handleApply = () => {
    onValuesChange(localValues);
    onApply(localValues);
    onClose();
  };

  const handleReset = () => {
    const resetValues: FilterValues = {};
    fields.forEach((field) => {
      if (field.type === "multiselect") {
        resetValues[field.key] = [];
      } else if (field.type === "toggle") {
        resetValues[field.key] = false;
      } else {
        resetValues[field.key] = "";
      }
    });
    setLocalValues(resetValues);
    onReset();
  };

  const renderField = (field: FilterField) => {
    const value = localValues[field.key];

    switch (field.type) {
      case "multiselect":
        return (
          <MultiSelectFieldComponent
            key={field.key}
            field={field}
            value={(value as string[]) || []}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "select":
        return (
          <SingleSelectFieldComponent
            key={field.key}
            field={field}
            value={(value as string) || ""}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "time":
        return (
          <TimeFieldComponent
            key={field.key}
            field={field}
            value={(value as string) || ""}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "toggle":
        return (
          <ToggleFieldComponent
            key={field.key}
            field={field}
            value={(value as boolean) || false}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Actionsheet isOpen={isOpen} onClose={onClose}>
      <ActionsheetBackdrop className="bg-black opacity-50" />
      <ActionsheetContent className="bg-white">
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>

        {/* Header */}
        <HStack className="items-center justify-between px-6 py-4 border-b border-outline-100">
          <Pressable onPress={onClose}>
            <Icon as={X} size="lg" className="text-typography-700" />
          </Pressable>
          <Text className="text-xl font-dm-sans-bold text-typography-900">
            {title}
          </Text>
          <Box className="w-6" />
        </HStack>

        {/* Filter Fields */}
        <VStack space="lg" className="px-6 py-6 flex-1">
          {fields.map(renderField)}
        </VStack>

        {/* Footer Actions */}
        <HStack space="md" className="px-6 py-6 border-t border-outline-100">
          <Button
            variant="outline"
            onPress={handleReset}
            className="flex-1 border-typography-300 bg-transparent rounded-full"
          >
            <ButtonText className="text-typography-600 font-dm-sans-medium">
              Reset all
            </ButtonText>
          </Button>
          <Button
            onPress={handleApply}
            className="flex-1 bg-[#00BFE0] rounded-full"
          >
            <ButtonText className="text-black font-dm-sans-medium">
              Apply
            </ButtonText>
          </Button>
        </HStack>
      </ActionsheetContent>
    </Actionsheet>
  );
};

FilterComponent.displayName = "FilterComponent";
