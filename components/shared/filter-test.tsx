import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { SafeAreaView } from "react-native-safe-area-context";
import { FilterComponent, FilterField, FilterValues } from "./filter-component";

// Simple test component to verify filter functionality
export const FilterTest = () => {
  const [showFilter, setShowFilter] = useState(false);
  const [filterValues, setFilterValues] = useState<FilterValues>({
    category: [],
    facility: "",
    onlyAvailable: false,
  });

  const filterFields: FilterField[] = [
    {
      type: "multiselect",
      key: "category",
      label: "Category",
      options: [
        { label: "Yoga", value: "yoga" },
        { label: "Pilates", value: "pilates" },
        { label: "Cardio", value: "cardio" },
      ],
    },
    {
      type: "select",
      key: "facility",
      label: "Facility",
      options: [
        { label: "Main Gym", value: "main-gym" },
        { label: "Pool Area", value: "pool" },
      ],
    },
    {
      type: "toggle",
      key: "onlyAvailable",
      label: "Only show what is available",
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1 p-4" space="lg">
        <Text className="text-2xl font-dm-sans-bold text-typography-900">
          Filter Test
        </Text>
        
        <Button onPress={() => setShowFilter(true)}>
          <ButtonText>Open Filter</ButtonText>
        </Button>

        <VStack space="md">
          <Text className="text-lg font-dm-sans-medium text-typography-700">
            Current Filter Values:
          </Text>
          <Text className="text-sm font-dm-sans-regular text-typography-600 bg-background-50 p-4 rounded-lg">
            {JSON.stringify(filterValues, null, 2)}
          </Text>
        </VStack>

        <FilterComponent
          isOpen={showFilter}
          onClose={() => setShowFilter(false)}
          fields={filterFields}
          values={filterValues}
          onValuesChange={setFilterValues}
          onApply={(values) => {
            console.log("Applied filters:", values);
            setFilterValues(values);
          }}
          onReset={() => {
            console.log("Reset filters");
            setFilterValues({
              category: [],
              facility: "",
              onlyAvailable: false,
            });
          }}
          title="Test Filter"
        />
      </VStack>
    </SafeAreaView>
  );
};
