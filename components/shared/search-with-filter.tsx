import React, { useState } from "react";
import { SearchNormal1, Setting4 } from "iconsax-react-nativejs";
import { HStack } from "../ui/hstack";
import { Input, InputField, InputIcon, InputSlot } from "../ui/input";
import { Pressable } from "react-native";
import { Icon } from "../ui/icon";
import { FilterComponent, FilterField, FilterValues } from "./filter-component";
import { Badge, BadgeText } from "../ui/badge";

export interface SearchWithFilterProps {
  onSearch?: (text: string) => void;
  searchTerm?: string;
  placeholder?: string;
  filterFields?: FilterField[];
  filterValues?: FilterValues;
  onFilterChange?: (values: FilterValues) => void;
  showFilterBadge?: boolean;
}

export const SearchWithFilter = ({
  onSearch,
  searchTerm,
  placeholder = "Search",
  filterFields = [],
  filterValues = {},
  onFilterChange,
  showFilterBadge = true,
}: SearchWithFilterProps) => {
  const [showFilter, setShowFilter] = useState(false);

  const handleFilterApply = (values: FilterValues) => {
    onFilterChange?.(values);
  };

  const handleFilterReset = () => {
    const resetValues: FilterValues = {};
    filterFields.forEach((field) => {
      if (field.type === "multiselect") {
        resetValues[field.key] = [];
      } else if (field.type === "toggle") {
        resetValues[field.key] = false;
      } else {
        resetValues[field.key] = "";
      }
    });
    onFilterChange?.(resetValues);
  };

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    Object.entries(filterValues).forEach(([key, value]) => {
      if (Array.isArray(value) && value.length > 0) {
        count += value.length;
      } else if (typeof value === "boolean" && value) {
        count += 1;
      } else if (typeof value === "string" && value.trim() !== "") {
        count += 1;
      }
    });
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <>
      <HStack className="gap-2 pl-4 pr-4">
        <Input className="h-10 flex-1 rounded-lg bg-white border" size="md">
          <InputSlot className="pl-4">
            <InputIcon
              as={() => <SearchNormal1 size="20" color="gray" />}
              className="text-typography-400"
              size="md"
              color="gray"
            />
          </InputSlot>
          <InputField
            placeholder={placeholder}
            className="placeholder:text-typography-400 focus:border-none"
            onChangeText={onSearch}
            value={searchTerm}
          />
        </Input>

        {filterFields.length > 0 && (
          <Pressable
            onPress={() => setShowFilter(true)}
            className={`w-10 h-10 rounded-full items-center justify-center border relative ${
              activeFilterCount > 0
                ? "bg-[#00BFE0] border-[#00BFE0]"
                : "bg-background-50 border-outline-200"
            }`}
          >
            <Icon
              color={activeFilterCount > 0 ? "white" : "gray"}
              as={() => (
                <Setting4
                  size="20"
                  color={activeFilterCount > 0 ? "white" : "gray"}
                />
              )}
              className={
                activeFilterCount > 0 ? "text-white" : "text-typography-600"
              }
              size="md"
            />

            {/* Filter count badge */}
            {showFilterBadge && activeFilterCount > 0 && (
              <Badge
                className="absolute -top-1 -right-1 bg-red-500 min-w-5 h-5 rounded-full items-center justify-center px-1"
                variant="solid"
              >
                <BadgeText className="text-white text-xs font-dm-sans-bold">
                  {activeFilterCount > 99
                    ? "99+"
                    : activeFilterCount.toString()}
                </BadgeText>
              </Badge>
            )}
          </Pressable>
        )}
      </HStack>

      {/* Filter Component */}
      {filterFields.length > 0 && (
        <FilterComponent
          isOpen={showFilter}
          onClose={() => setShowFilter(false)}
          fields={filterFields}
          values={filterValues}
          onValuesChange={onFilterChange || (() => {})}
          onApply={handleFilterApply}
          onReset={handleFilterReset}
        />
      )}
    </>
  );
};

SearchWithFilter.displayName = "SearchWithFilter";
