import { useCallback, useMemo, useState } from "react";
import { matchSorter } from "match-sorter";
import { useEventsQuery } from "@/data/screens/events/queries/useEventsQuery";
import { format } from "date-fns";
import { useFilter } from "./useFilter";
import { FilterField, FilterValues } from "@/components/shared/filter-component";
import { useClientInfo } from "@/data/screens/common/queries/useClientConfig";

export const useEventsWithFilter = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDate, setSelectedDate] = useState(new Date());
  
  const { data: clientData } = useClientInfo();

  // Define filter fields for events
  const filterFields: FilterField[] = useMemo(() => [
    {
      type: "multiselect",
      key: "category",
      label: "Category",
      options: [
        { label: "Workshops", value: "workshops" },
        { label: "Seminars", value: "seminars" },
        { label: "Social Events", value: "social" },
        { label: "Competitions", value: "competitions" },
        { label: "Health & Wellness", value: "health_wellness" },
        { label: "Sports Events", value: "sports" },
        { label: "Community Events", value: "community" },
      ],
    },
    {
      type: "select",
      key: "facility",
      label: "Facility",
      placeholder: "Select facility",
      options: clientData?.facilities.map(facility => ({
        label: facility.name,
        value: String(facility.id),
      })) || [],
    },
    {
      type: "time",
      key: "startTime",
      label: "Start time",
      placeholder: "Select start time",
      timeSlots: [
        { time_value: "06:00", time_label: "6:00 AM", allow_reservations: true },
        { time_value: "07:00", time_label: "7:00 AM", allow_reservations: true },
        { time_value: "08:00", time_label: "8:00 AM", allow_reservations: true },
        { time_value: "09:00", time_label: "9:00 AM", allow_reservations: true },
        { time_value: "10:00", time_label: "10:00 AM", allow_reservations: true },
        { time_value: "11:00", time_label: "11:00 AM", allow_reservations: true },
        { time_value: "12:00", time_label: "12:00 PM", allow_reservations: true },
        { time_value: "13:00", time_label: "1:00 PM", allow_reservations: true },
        { time_value: "14:00", time_label: "2:00 PM", allow_reservations: true },
        { time_value: "15:00", time_label: "3:00 PM", allow_reservations: true },
        { time_value: "16:00", time_label: "4:00 PM", allow_reservations: true },
        { time_value: "17:00", time_label: "5:00 PM", allow_reservations: true },
        { time_value: "18:00", time_label: "6:00 PM", allow_reservations: true },
        { time_value: "19:00", time_label: "7:00 PM", allow_reservations: true },
        { time_value: "20:00", time_label: "8:00 PM", allow_reservations: true },
        { time_value: "21:00", time_label: "9:00 PM", allow_reservations: true },
      ],
    },
    {
      type: "time",
      key: "endTime",
      label: "End time",
      placeholder: "Select end time",
      timeSlots: [
        { time_value: "07:00", time_label: "7:00 AM", allow_reservations: true },
        { time_value: "08:00", time_label: "8:00 AM", allow_reservations: true },
        { time_value: "09:00", time_label: "9:00 AM", allow_reservations: true },
        { time_value: "10:00", time_label: "10:00 AM", allow_reservations: true },
        { time_value: "11:00", time_label: "11:00 AM", allow_reservations: true },
        { time_value: "12:00", time_label: "12:00 PM", allow_reservations: true },
        { time_value: "13:00", time_label: "1:00 PM", allow_reservations: true },
        { time_value: "14:00", time_label: "2:00 PM", allow_reservations: true },
        { time_value: "15:00", time_label: "3:00 PM", allow_reservations: true },
        { time_value: "16:00", time_label: "4:00 PM", allow_reservations: true },
        { time_value: "17:00", time_label: "5:00 PM", allow_reservations: true },
        { time_value: "18:00", time_label: "6:00 PM", allow_reservations: true },
        { time_value: "19:00", time_label: "7:00 PM", allow_reservations: true },
        { time_value: "20:00", time_label: "8:00 PM", allow_reservations: true },
        { time_value: "21:00", time_label: "9:00 PM", allow_reservations: true },
        { time_value: "22:00", time_label: "10:00 PM", allow_reservations: true },
      ],
    },
    {
      type: "toggle",
      key: "onlyAvailable",
      label: "Only show what is available",
      description: "Show only events with available spots",
    },
    {
      type: "toggle",
      key: "onlyFavorites",
      label: "Show favorites only",
      description: "Show only your favorite events",
    },
    {
      type: "toggle",
      key: "freeEvents",
      label: "Free events only",
      description: "Show only free events",
    },
  ], [clientData?.facilities]);

  // Initialize filter hook
  const {
    filterValues,
    filterProps,
    hasActiveFilters,
    activeFilterCount,
    clearAllFilters,
  } = useFilter(filterFields, {
    onApply: (values) => {
      console.log("Applied event filters:", values);
    },
    onReset: () => {
      console.log("Reset event filters");
    },
  });

  // Fetch events data
  const {
    data: eventsData = [],
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useEventsQuery({
    date: format(selectedDate, "yyyy-MM"),
  });

  // Apply filtering
  const filteredData = useMemo(() => {
    let filtered = eventsData;

    // Apply search term
    if (searchTerm) {
      filtered = matchSorter(filtered, searchTerm, {
        keys: ["name", "gym_name", "room_name", "description"],
      });
    }

    // Apply facility filter
    if (filterValues.facility && typeof filterValues.facility === 'string') {
      filtered = filtered.filter(event => 
        String(event.gym_id) === filterValues.facility
      );
    }

    // Apply time filters
    if (filterValues.startTime && typeof filterValues.startTime === 'string') {
      filtered = filtered.filter(event => 
        event.start_time >= filterValues.startTime
      );
    }

    if (filterValues.endTime && typeof filterValues.endTime === 'string') {
      filtered = filtered.filter(event => 
        event.end_time <= filterValues.endTime
      );
    }

    // Apply availability filter
    if (filterValues.onlyAvailable) {
      filtered = filtered.filter(event => {
        const spotsLeft = Math.max(0, event.spots - event.reservation_count);
        return spotsLeft > 0;
      });
    }

    // Apply favorites filter
    if (filterValues.onlyFavorites) {
      filtered = filtered.filter(event => event.is_favourite);
    }

    // Apply free events filter
    if (filterValues.freeEvents) {
      filtered = filtered.filter(event => 
        !event.is_paid || parseFloat(event.price || '0') === 0
      );
    }

    // Apply category filter (client-side for now)
    if (filterValues.category && Array.isArray(filterValues.category) && filterValues.category.length > 0) {
      filtered = filtered.filter(event => {
        // This would need to be mapped to actual event categories from the API
        // For now, we'll use tags or description to match categories
        const eventText = `${event.name} ${event.description} ${event.tags}`.toLowerCase();
        return (filterValues.category as string[]).some(cat => {
          const categoryKeywords = {
            workshops: ['workshop', 'training', 'learn'],
            seminars: ['seminar', 'presentation', 'talk'],
            social: ['social', 'party', 'gathering', 'meet'],
            competitions: ['competition', 'contest', 'tournament', 'championship'],
            health_wellness: ['health', 'wellness', 'nutrition', 'mental'],
            sports: ['sport', 'game', 'match', 'league'],
            community: ['community', 'volunteer', 'service', 'outreach'],
          };
          
          const keywords = categoryKeywords[cat as keyof typeof categoryKeywords] || [];
          return keywords.some(keyword => eventText.includes(keyword));
        });
      });
    }

    return filtered;
  }, [eventsData, searchTerm, filterValues]);

  const handleDateChange = useCallback((date: Date) => {
    setSelectedDate(date);
  }, []);

  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchTerm("");
  }, []);

  const handleFilterChange = useCallback((values: FilterValues) => {
    // The filter hook handles this automatically
  }, []);

  return {
    // Data
    events: filteredData,
    isLoading,
    isRefetching,
    error,

    // UI State
    selectedDate,
    searchTerm,

    // Filter state
    filterValues,
    filterFields,
    hasActiveFilters,
    activeFilterCount,

    // Actions
    handleDateChange,
    handleSearch,
    setSearchTerm,
    clearSearch,
    refetch,
    handleFilterChange,
    clearAllFilters,

    // Filter props for components
    filterProps,
  };
};
